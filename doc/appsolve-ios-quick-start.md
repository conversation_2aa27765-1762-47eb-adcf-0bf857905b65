# ChatToDesign iOS App - Quick Start Guide

## 📋 Overview

ChatToDesign is a SwiftUI-based iOS application that enables users to generate and edit images through AI-powered design tools. The app features a clean architecture with modular design, Firebase integration, and subscription management through RevenueCat.

## 🎯 Key Features

- **AI Image Generation**: Generate images from text prompts
- **Image Editing**: Edit existing images with AI assistance
- **Asset Management**: Manage user-created assets and designs
- **Subscription System**: Premium features with RevenueCat integration
- **Social Authentication**: Google Sign-In and email authentication
- **Real-time Sync**: Firebase Firestore for data synchronization

## 🏗️ Architecture Overview

The app follows Clean Architecture principles with clear separation of concerns:

```
ChatToDesign/
├── Application/           # App entry point, delegates, modules
├── Domain/               # Business logic, entities, use cases
├── Infrastructure/       # External services, databases, APIs
├── Presentation/         # UI layer, views, view models
├── DesignSystem/        # UI components, tokens, themes
└── Documentation/       # Technical documentation
```

### Core Modules

- **Auth Module**: User authentication and session management
- **Design Module**: Image generation and editing workflows
- **Subscription Module**: Premium features and payment processing
- **Media Module**: Asset storage and management

## 🚀 Getting Started

### Prerequisites

- **Xcode 15.0+**
- **iOS 16.0+** deployment target
- **Swift 5.0+**
- **Firebase Project** (configured)
- **RevenueCat Account** (for subscriptions)
- **Google Cloud Project** (for authentication)

### 1. Clone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd picadabra-ios

# Open project in Xcode
open ChatToDesign.xcodeproj
```

### 2. Firebase Configuration

1. **Create Firebase Project**:

   - Go to [Firebase Console](https://console.firebase.google.com)
   - Create new project or use existing one
   - Enable Authentication, Firestore, Storage, Analytics

2. **Download Configuration**:

   - Download `GoogleService-Info.plist`
   - Replace existing file in `ChatToDesign/Application/`

3. **Configure Authentication**:

   - Enable Email/Password authentication
   - Enable Google Sign-In
   - Add iOS app with bundle ID: `com.a1d.chat-to-design`

4. **Setup Firestore Database**:
   - Create Firestore database in production mode
   - Configure security rules for your use case

### 3. Google Sign-In Setup

1. **Configure OAuth**:

   - Go to Google Cloud Console
   - Enable Google Sign-In API
   - Configure OAuth consent screen
   - Add authorized domains

2. **Update URL Schemes**:
   - Verify `Info.plist` contains correct URL scheme
   - Should match `REVERSED_CLIENT_ID` from GoogleService-Info.plist

### 4. RevenueCat Configuration

1. **Create RevenueCat Project**:

   - Sign up at [RevenueCat](https://www.revenuecat.com)
   - Create new project
   - Configure iOS app

2. **Setup Products**:

   - Create subscription products in App Store Connect
   - Configure products in RevenueCat dashboard
   - Set up entitlements (pro_tier with features)

3. **Update API Key**:
   - Get API key from RevenueCat dashboard
   - Configure in Firebase Remote Config or hardcode for testing

### 5. Third-Party Services

The app integrates several third-party services:

- **Firebase**: Authentication, Firestore, Storage, Analytics
- **RevenueCat**: Subscription management
- **Sentry**: Error reporting and monitoring
- **Moya**: Network layer abstraction
- **Kingfisher**: Image loading and caching
- **Lottie**: Animation support

### 6. Build and Run

1. **Install Dependencies**:

   ```bash
   # Dependencies are managed via Swift Package Manager
   # They will be automatically resolved when building
   ```

2. **Configure Build Settings**:

   - Set development team in project settings
   - Verify bundle identifier matches Firebase configuration
   - Ensure all required capabilities are enabled

3. **Build Project**:
   ```bash
   # Build from Xcode or command line
   xcodebuild -project ChatToDesign.xcodeproj -scheme ChatToDesign -configuration Debug
   ```

## 🔧 Configuration

### Environment Setup

1. **Development Environment**:

   - Use Debug configuration
   - Firebase debug logging enabled
   - Sentry debug mode
   - RevenueCat sandbox mode

2. **Production Environment**:
   - Use Release configuration
   - Disable debug logging
   - Production Firebase project
   - RevenueCat production mode

### Key Configuration Files

- `GoogleService-Info.plist`: Firebase configuration
- `Info.plist`: App metadata and permissions
- `ChatToDesign.entitlements`: App capabilities
- `RemoteConfigDefaults.plist`: Default remote config values

### Dependencies Management

The project uses Swift Package Manager for dependency management. Key dependencies include:

```swift
// Core Firebase Services
.package(url: "https://github.com/firebase/firebase-ios-sdk", from: "11.9.0")

// Networking
.package(url: "https://github.com/Moya/Moya.git", from: "15.0.3")

// Image Loading
.package(url: "https://github.com/onevcat/Kingfisher.git", from: "8.3.0")

// Subscription Management
.package(url: "https://github.com/RevenueCat/purchases-ios-spm.git", from: "5.28.1")

// Authentication
.package(url: "https://github.com/google/GoogleSignIn-iOS", from: "8.0.0")

// UI Components
.package(url: "https://github.com/airbnb/lottie-spm.git", from: "4.5.1")
.package(url: "https://github.com/exyte/Chat.git", from: "2.4.1")

// Error Reporting
.package(url: "https://github.com/getsentry/sentry-cocoa", from: "8.0.0")
```

### API Configuration

1. **Backend API Setup**:

   - Configure base URL in `APIEndpoint.swift`
   - Set up authentication tokens via `AccessTokenPlugin`
   - Configure request/response interceptors

2. **Firebase API Keys**:

   - Ensure all Firebase services are enabled
   - Configure API restrictions in Google Cloud Console
   - Set up proper security rules for Firestore

3. **Third-Party API Keys**:
   - RevenueCat API key (from dashboard)
   - Sentry DSN (for error reporting)
   - Any additional service API keys

### Remote Configuration

The app uses Firebase Remote Config for feature flags and configuration:

```swift
// Example remote config keys
"revenuecat_api_key": "your_revenuecat_api_key"
"enable_video_generation": true
"max_free_generations": 5
"paywall_display_delay": 2.0
```

## 📱 App Structure

### Main Navigation

The app uses a custom tab-based navigation with three main sections:

1. **Explore Page** (`ExplorePageView`):

   - Browse trending designs and templates
   - Search functionality
   - CMS-driven content

2. **Create Page** (`CreatePageView`):

   - AI image generation interface
   - Prompt input and customization
   - Real-time generation progress

3. **Profile Page** (`ProfilePageView`):
   - User profile management
   - Subscription status
   - Settings and preferences

### Authentication Flow

- **Login View**: Email and Google Sign-In options
- **Root View**: Handles authentication state transitions
- **User State Management**: Centralized user session handling

### Core Workflows

1. **Image Generation**:

   - User enters prompt
   - System processes request via API
   - Generated image displayed and saved

2. **Subscription Management**:
   - Paywall presentation
   - Purchase processing
   - Entitlement verification

## 🧪 Testing

### Running Tests

```bash
# Run unit tests
xcodebuild test -project ChatToDesign.xcodeproj -scheme ChatToDesign -destination 'platform=iOS Simulator,name=iPhone 15'

# Run specific test target
xcodebuild test -project ChatToDesign.xcodeproj -scheme ChatToDesign -only-testing:ChatToDesignTests
```

### Test Structure

- Unit tests for business logic and use cases
- Integration tests for Firebase services
- UI tests for critical user flows
- Mock implementations for external dependencies

### Development Workflow

1. **Local Development Setup**:

   ```bash
   # Create feature branch
   git checkout -b feature/your-feature-name

   # Install dependencies (automatic with Xcode)
   # Build and run
   open ChatToDesign.xcodeproj
   ```

2. **Code Organization**:

   - Follow Clean Architecture principles
   - Use dependency injection via `AppDependencyContainer`
   - Implement proper error handling and logging
   - Follow SwiftUI best practices

3. **Debugging Tools**:

   - **Firebase Debug View**: Monitor real-time database changes
   - **RevenueCat Debug Logs**: Track subscription events
   - **Sentry Integration**: Monitor crashes and errors
   - **Xcode Instruments**: Profile performance issues

4. **Development Best Practices**:
   - Use `Logger` for consistent logging across the app
   - Implement proper error handling with domain-specific errors
   - Follow MVVM pattern for UI components
   - Use async/await for asynchronous operations

### Logging and Monitoring

The app includes comprehensive logging and monitoring:

```swift
// Logging examples
Logger.info("User authentication successful")
Logger.error("Failed to generate image: \(error.localizedDescription)")
Logger.debug("API request completed with status: \(statusCode)")

// Analytics tracking
analyticsService.trackUserSignIn(method: "google", userId: userId)
analyticsService.trackError(code: "generation_failed", message: error.localizedDescription)
```

### Performance Optimization

1. **Image Loading**:

   - Use Kingfisher for efficient image caching
   - Implement proper image resizing and compression
   - Use lazy loading for large image lists

2. **Memory Management**:

   - Proper use of `@StateObject` and `@ObservedObject`
   - Avoid retain cycles in closures
   - Use weak references where appropriate

3. **Network Optimization**:
   - Implement request caching via Moya
   - Use proper timeout configurations
   - Handle network errors gracefully

## 🚀 Deployment

### App Store Preparation

1. **Configure Release Build**:

   - Set Release configuration
   - Update version and build numbers
   - Verify all certificates and provisioning profiles

2. **Archive and Upload**:

   ```bash
   # Archive for distribution
   xcodebuild archive -project ChatToDesign.xcodeproj -scheme ChatToDesign -archivePath ChatToDesign.xcarchive

   # Upload to App Store Connect
   xcodebuild -exportArchive -archivePath ChatToDesign.xcarchive -exportPath . -exportOptionsPlist ExportOptions.plist
   ```

### Production Checklist

- [ ] Firebase production project configured
- [ ] RevenueCat production setup complete
- [ ] All API keys and secrets secured
- [ ] App Store metadata and screenshots ready
- [ ] Privacy policy and terms of service updated
- [ ] Beta testing completed via TestFlight

## 📚 Additional Resources

### Documentation

- [Architecture Documentation](./02-Architecture/)
- [Design System Guide](./03-DesignSystem/)
- [API Integration Guide](./04-API/)
- [Deployment Guide](./05-Deployment/)

### External Documentation

- [Firebase iOS Setup](https://firebase.google.com/docs/ios/setup)
- [RevenueCat iOS Integration](https://docs.revenuecat.com/docs/ios)
- [SwiftUI Documentation](https://developer.apple.com/documentation/swiftui)

## 🆘 Troubleshooting

### Common Issues

1. **Build Errors**:

   - Clean build folder (`Cmd+Shift+K`)
   - Reset package caches (`File > Packages > Reset Package Caches`)
   - Verify all dependencies are resolved
   - Check for conflicting Swift versions
   - Ensure Xcode Command Line Tools are installed

2. **Firebase Connection Issues**:

   - Verify GoogleService-Info.plist is correctly placed in `ChatToDesign/Application/`
   - Check bundle identifier matches Firebase configuration exactly
   - Ensure Firebase services are enabled in console
   - Verify Firebase SDK version compatibility
   - Check network connectivity and firewall settings

3. **Authentication Problems**:

   - Verify Google Sign-In configuration in Google Cloud Console
   - Check URL schemes in Info.plist match GoogleService-Info.plist
   - Ensure OAuth consent screen is configured and published
   - Verify app is added to authorized domains
   - Check for proper certificate configuration

4. **Subscription Issues**:

   - Verify RevenueCat API key is correct and active
   - Check App Store Connect product configuration
   - Ensure sandbox testing is properly set up
   - Verify product identifiers match exactly
   - Check subscription group configuration

5. **Runtime Crashes**:
   - Check Sentry dashboard for crash reports
   - Use Xcode debugger to identify crash location
   - Verify all force unwraps are safe
   - Check for memory leaks using Instruments
   - Ensure proper error handling in async operations

### Debug Configuration

Enable detailed logging for troubleshooting:

```swift
// In AppDelegate or early in app lifecycle
#if DEBUG
Logger.logLevel = .debug
Purchases.logLevel = .debug
// Enable Firebase debug logging
FirebaseApp.configure()
#endif
```

### Performance Issues

1. **Slow Image Loading**:

   - Check network connection quality
   - Verify Kingfisher cache configuration
   - Monitor memory usage during image operations
   - Consider image compression settings

2. **UI Responsiveness**:

   - Ensure heavy operations are on background queues
   - Use `@MainActor` appropriately for UI updates
   - Profile with Instruments to identify bottlenecks
   - Check for excessive view redraws

3. **Memory Warnings**:
   - Monitor memory usage in Xcode debugger
   - Check for retain cycles in view models
   - Verify proper image cache limits
   - Use memory profiling tools

### Development Tips

1. **Code Quality**:

   - Use SwiftLint for consistent code style
   - Implement proper error handling patterns
   - Write unit tests for business logic
   - Use dependency injection for testability

2. **Architecture Best Practices**:

   - Keep view models focused and testable
   - Use proper separation of concerns
   - Implement repository pattern for data access
   - Follow SOLID principles

3. **Security Considerations**:
   - Never commit API keys or secrets to version control
   - Use Firebase Remote Config for sensitive configuration
   - Implement proper input validation
   - Use HTTPS for all network communications

### Getting Help

- Check existing documentation in `/Documentation/`
- Review Firebase and RevenueCat console logs
- Use Xcode debugger and console for runtime issues
- Consult third-party service documentation
- Check GitHub issues for known problems
- Use Xcode's built-in documentation viewer

---

**Note**: This guide provides the essential steps to get started. For detailed implementation guides and advanced configuration, refer to the specific documentation files in the `/Documentation/` directory.
