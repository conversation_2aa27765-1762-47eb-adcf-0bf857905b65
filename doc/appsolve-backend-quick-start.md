# Chat-to-Design Backend Quick Start Guide

Welcome to the Chat-to-Design Backend! This guide will help you get up and running quickly with the development environment.

## 🎯 What is This Project?

Chat-to-Design Backend is a comprehensive AI-powered content generation service built with:
- **Framework**: Hono.js running on Cloudflare Workers
- **Language**: TypeScript
- **Database**: Firebase Firestore
- **Storage**: Cloudflare R2
- **AI Services**: Google Gemini, OpenAI, and multiple AI providers
- **Authentication**: Firebase Authentication

## 🚀 Quick Setup (5 minutes)

### Prerequisites

Before you start, make sure you have:
- **Node.js 18+** installed
- **pnpm** package manager (`npm install -g pnpm`)
- **Git** for version control

### Step 1: Clone and Install

```bash
# Clone the repository
git clone <repository-url>
cd chat-to-design-backend

# Install dependencies
pnpm install
```

### Step 2: Environment Configuration

Create a `.dev.vars` file in the project root for local development:

```bash
# Copy the example file
cp .env.example .dev.vars
```

Edit `.dev.vars` with your configuration:

```bash
# Firebase Configuration (Required)
FIREBASE_PROJECT_ID="your-firebase-project-id"
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL="<EMAIL>"

# AI Services (Optional for basic testing)
GOOGLE_API_KEY="your-google-gemini-api-key"
OPENAI_API_KEY="your-openai-api-key"

# Storage (Optional for basic testing)
R2_BUCKET_NAME="your-cloudflare-r2-bucket"
R2_ACCESS_KEY_ID="your-r2-access-key"
R2_SECRET_ACCESS_KEY="your-r2-secret-key"

# Rate Limiting (Optional)
UPSTASH_REDIS_REST_URL="your-upstash-redis-url"
UPSTASH_REDIS_REST_TOKEN="your-upstash-redis-token"

# Notion CMS (Optional)
NOTION_AUTH_TOKEN="your-notion-integration-token"
NOTION_DATABASE_ID="your-notion-database-id"
```

### Step 3: Start Development Server

```bash
pnpm run dev
```

The server will start at `http://localhost:8787`

### Step 4: Test the API

Visit `http://localhost:8787/docs` to see the interactive API documentation (Swagger UI).

## 🏗️ Project Structure Overview

```
src/
├── features/              # Feature modules (domain-driven design)
│   ├── assets/           # Asset management
│   ├── auth/             # Authentication & authorization
│   ├── explore/          # Public content discovery
│   ├── image-generation/ # AI image generation
│   ├── video-generation/ # AI video generation
│   ├── social/           # Social features (likes, favorites)
│   ├── user/             # User management
│   └── webhook/          # External webhooks
├── infrastructure/        # Core infrastructure services
│   ├── ai/               # AI service providers
│   ├── cache/            # Caching layer
│   ├── db/               # Database services
│   ├── env/              # Environment configuration
│   └── storage/          # File storage services
├── lib/                  # Shared utilities
├── middleware/           # HTTP middleware
└── types/                # TypeScript type definitions
```

## 🔧 Essential Configuration

### Firebase Setup (Required)

1. Create a Firebase project at https://console.firebase.google.com
2. Enable Firestore Database
3. Enable Authentication
4. Generate a service account key:
   - Go to Project Settings → Service Accounts
   - Click "Generate new private key"
   - Extract the values for `.dev.vars`

### Cloudflare Setup (For deployment)

1. Install Wrangler CLI: `npm install -g wrangler`
2. Login to Cloudflare: `wrangler login`
3. Update `wrangler.jsonc` with your account details

## 🧪 Testing Your Setup

### 1. Health Check
```bash
curl http://localhost:8787/api/v1/health
```

### 2. Explore Public Content
```bash
curl http://localhost:8787/api/v1/explore
```

### 3. Check API Documentation
Open `http://localhost:8787/docs` in your browser

## 🚀 Common Development Tasks

### Adding a New Feature

1. Create a new directory in `src/features/your-feature/`
2. Follow the existing pattern:
   ```
   your-feature/
   ├── your-feature.interface.ts    # Service interface
   ├── your-feature.service.ts      # Business logic
   ├── your-feature.schema.ts       # Zod validation schemas
   ├── your-feature.routes.ts       # Route definitions
   └── endpoints/                   # Individual endpoint handlers
   ```

### Running Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch
```

### Building for Production

```bash
pnpm run build
```

### Deploying

```bash
# Deploy to test environment
pnpm run deploy

# Deploy to production
pnpm run deploy:prod
```

## 🔍 Key Features Overview

### 🎨 AI Content Generation
- **Image Generation**: Multiple AI providers (OpenAI DALL-E, Google Imagen, etc.)
- **Video Generation**: AI-powered video creation
- **Prompt Engineering**: Optimized prompts for better results

### 📁 Asset Management
- **File Upload**: Secure file upload to Cloudflare R2
- **Metadata Management**: Rich metadata for all assets
- **Public/Private Assets**: Flexible visibility controls

### 👥 Social Features
- **Likes & Favorites**: User interaction tracking
- **Explore Feed**: Public content discovery
- **User Profiles**: User-generated content management

### 🔐 Authentication & Authorization
- **Firebase Auth**: Secure user authentication
- **Role-based Access**: Feature-based permissions
- **Quota Management**: Usage tracking and limits

## 🛠️ Development Tools

### Dependency Injection
The project uses `tsyringe` for dependency injection. Services are registered in `src/container.setup.ts`.

### API Documentation
Automatic OpenAPI/Swagger documentation is generated using `chanfana`.

### Environment Management
Environment variables are managed through Cloudflare Workers bindings and local `.dev.vars`.

## 📚 Next Steps

1. **Read the Architecture Docs**: Check out `docs/` for detailed feature documentation
2. **Explore the API**: Use the Swagger UI at `/docs` to understand available endpoints
3. **Check Feature READMEs**: Each feature has its own README with detailed information
4. **Join Development**: Follow the patterns established in existing features

## 🆘 Troubleshooting

### Common Issues

**Port already in use**:
```bash
# Kill the process using port 8787
lsof -ti:8787 | xargs kill -9
```

**Firebase connection issues**:
- Verify your service account key format
- Check that Firestore is enabled in your Firebase project
- Ensure the service account has proper permissions

**Missing dependencies**:
```bash
# Clean install
rm -rf node_modules pnpm-lock.yaml
pnpm install
```

### Getting Help

- Check existing documentation in `docs/`
- Review feature-specific READMEs
- Look at existing code patterns in `src/features/`
- Check the API documentation at `/docs`

## 🎉 You're Ready!

You now have a working development environment. Start exploring the codebase and building amazing features!

Happy coding! 🚀
