# iOS Quick Start

This guide will help you quickly set up and run the AppSolve iOS application. AppSolve is a powerful AI content generation app template that supports image generation, editing, subscription management, and more.

## Prerequisites

Before starting, make sure your development environment meets the following requirements:

- **macOS** 13.0 or higher
- **Xcode** 15.0 or higher
- **iOS** 16.0+ deployment target
- **Swift** 5.0+
- Valid **Apple Developer Account**
- **Firebase** project (for authentication, database, and storage)
- **RevenueCat** account (for subscription management)

## Quick Setup

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/appsolve-ios.git
cd appsolve-ios
```

### 2. Install Dependencies

```bash
# Install CocoaPods dependencies
pod install

# Open the workspace
open AppSolve.xcworkspace
```

### 3. Configure Firebase

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or select existing one
3. Add iOS app to your Firebase project
4. Download `GoogleService-Info.plist`
5. Add the file to your Xcode project

### 4. Configure RevenueCat

1. Sign up at [RevenueCat](https://www.revenuecat.com/)
2. Create a new project
3. Add your API key to the configuration

### 5. Build and Run

1. Select your target device or simulator
2. Press `Cmd + R` to build and run
3. The app should launch successfully

## Next Steps

- Configure your AI services
- Set up subscription products
- Customize the app design
- Test the core functionality

For detailed configuration, see the [Configuration Guide](/docs/configuration-ios).
