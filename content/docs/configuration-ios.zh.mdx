# iOS 配置指南

本指南提供 AppSolve iOS 应用的详细配置说明，包括 Firebase、RevenueCat、推送通知、App Store 等配置。

## Firebase 配置

### 1. Authentication 配置

#### 1.1 启用认证方式

在 Firebase Console > Authentication > Sign-in method 中启用：

- **Email/Password**：基础认证方式
- **Google Sign-In**：
  1. 启用 Google 提供方
  2. 配置 OAuth 同意屏幕
  3. 添加支持邮箱

#### 1.2 配置 OAuth 重定向

在 `Info.plist` 中添加 URL Schemes：

```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLSchemes</key>
        <array>
            <!-- 从 GoogleService-Info.plist 复制 REVERSED_CLIENT_ID -->
            <string>com.googleusercontent.apps.YOUR_REVERSED_CLIENT_ID</string>
        </array>
    </dict>
</array>
```

### 2. Firestore 数据库配置

#### 2.1 数据结构

```
firestore/
├── users/                 # 用户信息
│   └── {userId}/
│       ├── profile       # 用户资料
│       └── settings      # 用户设置
├── assets/               # 用户资产
│   └── {assetId}/
├── chats/                # 聊天记录
│   └── {chatId}/
│       └── messages/     # 消息子集合
├── imageTasks/           # 图像生成任务
│   └── {taskId}/
└── videoTasks/           # 视频生成任务
    └── {taskId}/
```

#### 2.2 索引配置

创建以下复合索引以优化查询性能：

```javascript
// assets 集合
- userId (升序) + createdAt (降序)
- userId (升序) + type (升序) + createdAt (降序)

// imageTasks 集合  
- userId (升序) + status (升序) + createdAt (降序)

```

#### 3.2 文件组织

- `/users/{userId}/uploads/` - 用户上传的原始文件
- `/generated/{userId}/images/` - AI 生成的图像
- `/generated/{userId}/videos/` - AI 生成的视频

## RevenueCat 配置

### 1. 产品配置

#### 1.1 在 App Store Connect 创建产品

创建以下订阅产品：

```
- appsolve_pro_monthly    # 专业版月订阅
- appsolve_pro_yearly     # 专业版年订阅
- appsolve_premium_monthly # 高级版月订阅
- appsolve_premium_yearly  # 高级版年订阅
```

#### 1.2 配置权益（Entitlements）

在 RevenueCat Dashboard 中创建权益：

```swift
// Domain/Entities/Entitlement.swift
enum EntitlementType: String {
    case free = "free"
    case pro = "pro" 
    case premium = "premium"
}
```

#### 1.3 配置 Offerings

创建默认 Offering，包含所有订阅选项：

```
Default Offering:
├── Monthly Pro ($9.99)
├── Yearly Pro ($99.99) - Save 17%
├── Monthly Premium ($19.99)
└── Yearly Premium ($199.99) - Save 17%
```

### 2. 代码集成

#### 2.1 初始化 RevenueCat

```swift
// ChatToDesignApp.swift
import RevenueCat

@main
struct ChatToDesignApp: App {
    init() {
        Purchases.logLevel = .debug
        Purchases.configure(withAPIKey: Constants.revenueCatAPIKey)
    }
}
```

#### 2.2 权限检查

```swift
// SubscriptionService.swift
func checkSubscriptionStatus() async -> UserTier {
    do {
        let customerInfo = try await Purchases.shared.customerInfo()
        
        if customerInfo.entitlements["premium"]?.isActive == true {
            return .premium
        } else if customerInfo.entitlements["pro"]?.isActive == true {
            return .pro
        } else {
            return .free
        }
    } catch {
        return .free
    }
}
```

## 推送通知配置

### 1. 启用推送功能

#### 1.1 在 Xcode 中启用

1. 选择项目 Target
2. Signing & Capabilities
3. 添加 "Push Notifications" capability
4. 添加 "Background Modes" capability
5. 勾选 "Remote notifications"

#### 1.2 创建推送证书

1. 在 Apple Developer 中创建 APNs 证书
2. 上传到 Firebase Console > Project Settings > Cloud Messaging

### 2. 代码实现

```swift
// AppDelegate.swift
import FirebaseMessaging

class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication,
                    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        
        // 配置 Firebase
        FirebaseApp.configure()
        
        // 配置推送
        Messaging.messaging().delegate = self
        UNUserNotificationCenter.current().delegate = self
        
        // 请求推送权限
        requestPushPermission()
        
        return true
    }
    
    func requestPushPermission() {
        let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
        UNUserNotificationCenter.current().requestAuthorization(
            options: authOptions,
            completionHandler: { _, _ in }
        )
        
        UIApplication.shared.registerForRemoteNotifications()
    }
}
```

## API 端点配置

### 1. 环境配置

```swift
// Utils/Constants.swift
struct Constants {
    // API 配置
    #if DEBUG
    static let apiBaseURL = "http://localhost:8787"
    #else
    static let apiBaseURL = "https://api.appsolve.com"
    #endif
    
    // 第三方服务
    static let sentryDSN = "your-sentry-dsn"
    static let googleClientID = "your-google-client-id"
}
```

### 2. 网络层配置

```swift
// Infrastructure/Network/NetworkService.swift
class NetworkService {
    private let session: URLSession
    
    init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 300
        config.waitsForConnectivity = true
        
        self.session = URLSession(configuration: config)
    }
}
```

## App Store 配置

### 1. App Store Connect 设置

#### 1.1 基本信息

- **Bundle ID**: `com.yourcompany.appsolve`
- **支持 URL**: `https://appsolve.com/support`
- **隐私政策 URL**: `https://appsolve.com/privacy`

#### 1.2 App 权限说明

在 `Info.plist` 中添加使用说明：

```xml
<!-- 相册访问 -->
<key>NSPhotoLibraryUsageDescription</key>
<string>AppSolve 需要访问您的相册来保存生成的图像和视频</string>

<key>NSPhotoLibraryAddUsageDescription</key>
<string>AppSolve 需要将生成的内容保存到您的相册</string>

<!-- 相机访问（如需要） -->
<key>NSCameraUsageDescription</key>
<string>AppSolve 需要访问相机来拍摄照片</string>
```

### 2. 深度链接配置

#### 2.1 Universal Links

1. 创建 `apple-app-site-association` 文件
2. 部署到 `https://yourdomain.com/.well-known/apple-app-site-association`

```json
{
    "applinks": {
        "apps": [],
        "details": [
            {
                "appID": "TEAMID.com.yourcompany.appsolve",
                "paths": [
                    "/share/*",
                    "/design/*",
                    "/user/*"
                ]
            }
        ]
    }
}
```

#### 2.2 在应用中处理

```swift
// ChatToDesignApp.swift
.onOpenURL { url in
    handleDeepLink(url)
}

func handleDeepLink(_ url: URL) {
    guard let components = URLComponents(url: url, resolvingAgainstBaseURL: true) else { return }
    
    switch components.path {
    case "/share":
        // 处理分享链接
    case "/design":
        // 打开设计详情
    case "/user":
        // 打开用户资料
    default:
        break
    }
}
```

## 性能优化配置

### 1. 图片缓存

```swift
// Infrastructure/Cache/ImageCacheManager.swift
class ImageCacheManager {
    static let shared = ImageCacheManager()
    
    private let cache = NSCache<NSString, UIImage>()
    private let diskCacheURL: URL
    
    init() {
        cache.countLimit = 100 // 最多缓存 100 张图片
        cache.totalCostLimit = 100 * 1024 * 1024 // 100MB
        
        // 磁盘缓存路径
        diskCacheURL = FileManager.default.urls(for: .cachesDirectory, 
                                               in: .userDomainMask)[0]
            .appendingPathComponent("ImageCache")
    }
}
```

### 2. 视频预加载

```swift
// Infrastructure/Video/VideoPreloadService.swift
class VideoPreloadService {
    private let preloadQueue = OperationQueue()
    
    init() {
        preloadQueue.maxConcurrentOperationCount = 2
        preloadQueue.qualityOfService = .background
    }
}
```

## 安全配置

### 1. App Transport Security

```xml
<!-- Info.plist -->
<key>NSAppTransportSecurity</key>
<dict>
    <!-- 仅在开发环境允许 HTTP -->
    <key>NSAllowsLocalNetworking</key>
    <true/>
    
    <!-- 生产环境强制 HTTPS -->
    <key>NSAllowsArbitraryLoads</key>
    <false/>
</dict>
```

### 2. Keychain 存储

```swift
// 存储敏感信息
KeychainHelper.save(key: "api_token", value: token)
KeychainHelper.get(key: "api_token")
KeychainHelper.delete(key: "api_token")
```

## 故障排除

### 常见配置问题

1. **Firebase 初始化失败**
   - 检查 `GoogleService-Info.plist` 是否正确添加到项目
   - 确认 Bundle ID 与 Firebase 项目匹配

2. **RevenueCat 产品加载失败**
   - 确认产品已在 App Store Connect 中创建并批准
   - 检查 RevenueCat API Key 是否正确
   - 使用沙盒账户测试

3. **推送通知不工作**
   - 验证推送证书是否正确上传到 Firebase
   - 检查设备是否授予推送权限
   - 确认 FCM Token 是否成功获取

4. **深度链接无响应**
   - 验证 Associated Domains 配置
   - 检查服务器上的 apple-app-site-association 文件
   - 使用 Apple 的验证工具测试

## 下一步

- 查看 [后端配置指南](./configuration-backend.zh.mdx) 配置服务端
- 阅读 [部署指南](./deployment.zh.mdx) 了解发布流程
- 参考 [最佳实践](./best-practices.zh.mdx) 优化应用