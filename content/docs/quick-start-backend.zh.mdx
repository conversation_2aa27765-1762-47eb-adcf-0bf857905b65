
# 后端快速开始

本指南将帮助你快速搭建和运行 AppSolve 后端服务。后端基于 Cloudflare Workers 构建，提供高性能的 AI 内容生成 API。

## 前提条件

在开始之前，请确保你的开发环境满足以下要求：

- **Node.js** 18.0 或更高版本
- **pnpm** 8.0 或更高版本
- **Git**
- **Cloudflare** 账户（免费套餐即可开始）
- **Firebase** 项目（与 iOS 应用共享）
- AI 服务账户（可选）：
  - OpenAI API Key
  - Google AI (Gemini) API Key

## 步骤 1：克隆项目

```bash
# 克隆仓库
git clone https://github.com/your-org/appsolve-backend.git
cd appsolve-backend

# 安装 pnpm（如果未安装）
npm install -g pnpm
```

## 步骤 2：安装依赖

```bash
# 使用 pnpm 安装依赖
pnpm install
```

主要依赖包括：
- **Hono.js**：轻量级 Web 框架
- **Firebase Admin SDK**：服务端 Firebase 操作
- **Zod**：运行时类型验证
- **TSyringe**：依赖注入
- **@fal-ai/serverless-client**：AI 图像生成

## 步骤 3：配置 Firebase Admin

### 3.1 生成服务账号密钥

1. 访问 [Firebase Console](https://console.firebase.google.com)
2. 选择你的项目
3. 进入 **项目设置** > **服务账号**
4. 点击 **生成新的私钥**
5. 下载 JSON 文件并重命名为 `service-account-key.json`
6. 将文件放置在项目根目录

### 3.2 配置安全规则

在 Firebase Console 中配置 Firestore 安全规则：

```javascript
// 基础规则示例
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 用户只能访问自己的数据
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // 其他集合规则...
  }
}
```

## 步骤 4：配置环境变量

### 4.1 创建本地开发配置

```bash
# 复制示例配置文件
cp .env.example .dev.vars
```

### 4.2 编辑 `.dev.vars` 文件

```bash
# Firebase 配置
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# AI 服务配置（可选）
OPENAI_API_KEY=sk-...
GOOGLE_AI_API_KEY=AIza...
FAL_KEY=your-fal-ai-key

# 其他配置
ENVIRONMENT=development
```

## 步骤 5：配置 Cloudflare

### 5.1 安装 Wrangler CLI

```bash
# Wrangler 已包含在项目依赖中
# 验证安装
pnpm wrangler --version
```

### 5.2 登录 Cloudflare

```bash
pnpm wrangler login
```

### 5.3 创建 KV 命名空间

```bash
# 创建缓存存储
pnpm wrangler kv:namespace create "CACHE"
pnpm wrangler kv:namespace create "CACHE" --preview

```

### 5.4 创建 R2 存储桶

```bash
# 创建媒体存储桶
pnpm wrangler r2 bucket create media-storage
```

### 5.5 更新 `wrangler.jsonc`

将创建的资源 ID 更新到配置文件中：

```json
{
  "name": "appsolve-backend",
  "kv_namespaces": [
    {
      "binding": "CACHE",
      "id": "your-cache-namespace-id",
      "preview_id": "your-cache-preview-id"
    },
    {
      "binding": "ANALYTICS", 
      "id": "your-analytics-namespace-id",
      "preview_id": "your-analytics-preview-id"
    }
  ],
  "r2_buckets": [
    {
      "binding": "R2_MEDIA_STORAGE",
      "bucket_name": "media-storage"
    }
  ]
}
```

## 步骤 6：运行开发服务器

```bash
# 启动本地开发服务器
pnpm dev

# 服务将在 http://localhost:8787 启动


```

## 步骤 7：测试 API

# 服务将在 http://localhost:8787/docs 是 OPENAPI 文档

### 7.2 测试认证（需要 Firebase ID Token）

```bash
# 获取 ID Token（从 iOS 应用或 Firebase Auth）
curl -H "Authorization: Bearer YOUR_ID_TOKEN" \
  http://localhost:8787/api/v1/user/profile
```


## 步骤 8：部署到生产环境

### 8.1 配置生产环境变量

```bash
# 设置生产环境密钥
pnpm wrangler secret put FIREBASE_PRIVATE_KEY
pnpm wrangler secret put OPENAI_API_KEY
pnpm wrangler secret put GOOGLE_AI_API_KEY
```

### 8.2 部署

```bash
# 部署到 Cloudflare Workers
pnpm deploy

# 部署成功后会显示 URL
# https://appsolve-backend.your-subdomain.workers.dev
```

## 项目结构

```
appsolve-backend/
├── src/
│   ├── features/          # 功能模块
│   │   ├── auth/         # 认证
│   │   ├── assets/       # 资产管理
│   │   ├── explore/      # 探索功能
│   │   ├── image-generation/  # AI 图像生成
│   │   └── video-generation/  # AI 视频生成
│   ├── infrastructure/    # 基础设施
│   │   ├── ai/           # AI 服务集成
│   │   ├── cache/        # 缓存服务
│   │   └── db/           # 数据库服务
│   └── index.ts          # 应用入口
├── wrangler.jsonc        # Cloudflare 配置
└── package.json          # 项目配置
```

## 常见问题

### 本地开发问题

1. **端口被占用**
   ```bash
   # 使用不同端口
   pnpm dev --port 8788
   ```

2. **Firebase 认证失败**
   - 检查服务账号密钥是否正确
   - 确认 Firebase 项目 ID 匹配

### Cloudflare 部署问题

1. **部署大小限制**
   - Workers 免费套餐限制 1MB
   - 考虑使用 Workers Paid 计划

2. **环境变量未生效**
   - 使用 `wrangler secret list` 检查
   - 重新部署以应用更改

### AI 服务问题

1. **API 限额**
   - 监控使用量
   - 实现请求限流
   - 使用缓存减少重复请求

## 下一步

- 查看 [后端配置指南](./configuration-backend.zh.mdx) 了解详细配置
- 阅读 [API 文档](./api-reference.zh.mdx) 了解所有端点
- 探索高级功能如缓存策略、限流配置等

## 获取帮助

如果遇到问题：
1. 查看 `docs/` 目录中的详细文档
2. 检查 Cloudflare Workers 日志
3. 在项目 Issues 中搜索或提交问题