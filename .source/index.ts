// @ts-nocheck -- skip type checking
import * as docs_10 from "../content/docs/quick-start-ios.zh.mdx?collection=docs&hash=1752567730927"
import * as docs_9 from "../content/docs/quick-start-ios.mdx?collection=docs&hash=1752567730927"
import * as docs_8 from "../content/docs/quick-start-backend.zh.mdx?collection=docs&hash=1752567730927"
import * as docs_7 from "../content/docs/quick-start-backend.mdx?collection=docs&hash=1752567730927"
import * as docs_6 from "../content/docs/overview.mdx?collection=docs&hash=1752567730927"
import * as docs_5 from "../content/docs/index.zh.mdx?collection=docs&hash=1752567730927"
import * as docs_4 from "../content/docs/index.mdx?collection=docs&hash=1752567730927"
import * as docs_3 from "../content/docs/configuration-ios.zh.mdx?collection=docs&hash=1752567730927"
import * as docs_2 from "../content/docs/configuration-ios.mdx?collection=docs&hash=1752567730927"
import * as docs_1 from "../content/docs/configuration-backend.zh.mdx?collection=docs&hash=1752567730927"
import * as docs_0 from "../content/docs/configuration-backend.mdx?collection=docs&hash=1752567730927"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"configuration-backend.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/configuration-backend.mdx"}, data: docs_0 }, { info: {"path":"configuration-backend.zh.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/configuration-backend.zh.mdx"}, data: docs_1 }, { info: {"path":"configuration-ios.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/configuration-ios.mdx"}, data: docs_2 }, { info: {"path":"configuration-ios.zh.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/configuration-ios.zh.mdx"}, data: docs_3 }, { info: {"path":"index.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/index.mdx"}, data: docs_4 }, { info: {"path":"index.zh.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/index.zh.mdx"}, data: docs_5 }, { info: {"path":"overview.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/overview.mdx"}, data: docs_6 }, { info: {"path":"quick-start-backend.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/quick-start-backend.mdx"}, data: docs_7 }, { info: {"path":"quick-start-backend.zh.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/quick-start-backend.zh.mdx"}, data: docs_8 }, { info: {"path":"quick-start-ios.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/quick-start-ios.mdx"}, data: docs_9 }, { info: {"path":"quick-start-ios.zh.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/quick-start-ios.zh.mdx"}, data: docs_10 }], [{"info":{"path":"meta.json","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/meta.json"},"data":{"pages":["overview","quick-start-ios","quick-start-backend","configuration-ios","configuration-backend"]}}, {"info":{"path":"meta.zh.json","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/meta.zh.json"},"data":{"pages":["overview","quick-start-ios","quick-start-backend","configuration-ios","configuration-backend"]}}])